import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 font-[family-name:var(--font-geist-sans)]">
      <main className="text-center">
        {/* Title positioned at top fold */}
        <div className="mb-8 lg:mb-12">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-neutral-500 tracking-tight">
            React Next.js Learn Auth
          </h1>
        </div>

        {/* Dashboard button with authentication button styling */}
        <div className="flex justify-center">
          <Link
            href="/dashboard"
            className="flex items-center justify-center gap-3 rounded-lg bg-white px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium text-gray-700 shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 8.976C3 4.05476 4.05476 3 8.976 3H15.024C19.9452 3 21 4.05476 21 8.976V15.024C21 19.9452 19.9452 21 15.024 21H8.976C4.05476 21 3 19.9452 3 15.024V8.976Z" stroke="#323232" strokeWidth="2"/>
              <path d="M21 9L3 9" stroke="#323232" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M9 21L9 9" stroke="#323232" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            Dashboard
          </Link>
        </div>
      </main>
    </div>
  );
}
